package com.pharmacy.management.api;

import com.pharmacy.management.dto.ApiResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器
 * 用于验证API基础架构是否正常工作
 */
@RestController
@RequestMapping("/test")
public class TestController {
    
    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public ApiResponse<Map<String, Object>> health() {
        Map<String, Object> data = new HashMap<>();
        data.put("status", "UP");
        data.put("timestamp", LocalDateTime.now());
        data.put("message", "API服务正常运行");
        
        return ApiResponse.success("健康检查通过", data);
    }
    
    /**
     * 测试异常处理
     */
    @GetMapping("/error")
    public ApiResponse<Void> testError() {
        throw new RuntimeException("这是一个测试异常");
    }
    
    /**
     * 测试参数验证
     */
    @GetMapping("/validate")
    public ApiResponse<String> testValidate() {
        throw new IllegalArgumentException("参数验证失败");
    }
}
