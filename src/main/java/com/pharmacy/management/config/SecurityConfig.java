package com.pharmacy.management.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.crypto.password.NoOpPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class SecurityConfig extends WebSecurityConfigurerAdapter {

    @Bean
    public PasswordEncoder passwordEncoder() {
        return NoOpPasswordEncoder.getInstance();
    }

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http
            .csrf().disable()
            .authorizeRequests()
                // 静态资源
                .antMatchers("/static/**", "/css/**", "/js/**", "/img/**",
                            "/plugins/**", "/dist/**", "/build/**", "/vendor/**").permitAll()
                // API接口 - 暂时允许所有访问，后续会添加JWT认证
                .antMatchers("/api/**").permitAll()
                // 登录相关页面
                .antMatchers("/login", "/home", "/pharmacy/select", "/pharmacy/switch/**").permitAll()
                .antMatchers("/register").permitAll()
                // 管理员权限页面
                .antMatchers("/admin/**").hasRole("ADMIN")
                .antMatchers("/users/**").hasRole("ADMIN")
                .antMatchers("/pharmacies/**").hasRole("ADMIN")
                .antMatchers("/statistics/**").hasRole("ADMIN")
                .antMatchers("/migration/**").hasRole("ADMIN")
                .anyRequest().authenticated()
                .and()
            .formLogin()
                .loginPage("/login")
                .defaultSuccessUrl("/")
                .permitAll()
                .and()
            .logout()
                .logoutSuccessUrl("/login?logout")
                .permitAll();
    }
} 