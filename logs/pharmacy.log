2025-05-29 13:11:12.829  INFO 94282 --- [main] c.p.m.PharmacyManagementApplication      : Starting PharmacyManagementApplication using Java 1.8.0_421 on ZBMAC-061e5e91a with PID 94282 (/Users/<USER>/ai/pharmacy_online/pharmacy/target/classes started by liandahu in /Users/<USER>/ai/pharmacy_online/pharmacy)
2025-05-29 13:11:12.840 DEBUG 94282 --- [main] c.p.m.PharmacyManagementApplication      : Running with Spring Boot v2.5.14, Spring v5.3.20
2025-05-29 13:11:12.857  INFO 94282 --- [main] c.p.m.PharmacyManagementApplication      : The following 1 profile is active: "dev"
2025-05-29 13:11:13.567  INFO 94282 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-29 13:11:13.670  INFO 94282 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 92 ms. Found 8 JPA repository interfaces.
2025-05-29 13:11:14.003  INFO 94282 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@23b8d9f3' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 13:11:14.006  INFO 94282 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 13:11:14.267  INFO 94282 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-05-29 13:11:14.278  INFO 94282 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-05-29 13:11:14.278  INFO 94282 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.63]
2025-05-29 13:11:14.429  INFO 94282 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-05-29 13:11:14.430  INFO 94282 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1489 ms
2025-05-29 13:11:14.486 DEBUG 94282 --- [main] o.s.w.s.r.ResourceUrlEncodingFilter      : Filter 'resourceUrlEncodingFilter' configured for use
2025-05-29 13:11:14.524  INFO 94282 --- [main] c.p.management.config.DataSourceConfig   : 已添加maxAllowedPacket参数到JDBC URL: ********************************************************************************************************************************************************************************
2025-05-29 13:11:14.525  INFO 94282 --- [main] c.p.management.config.DataSourceConfig   : 已调整连接池最大生命周期为: 600000ms (10分钟)
2025-05-29 13:11:14.525  INFO 94282 --- [main] c.p.management.config.DataSourceConfig   : 已设置连接超时时间为: 30000ms (30秒)
2025-05-29 13:11:14.525  INFO 94282 --- [main] c.p.management.config.DataSourceConfig   : 已设置空闲超时为: 120000ms (2分钟)
2025-05-29 13:11:14.525  INFO 94282 --- [main] c.p.management.config.DataSourceConfig   : 已设置连接测试查询
2025-05-29 13:11:14.527  INFO 94282 --- [main] c.p.management.config.DataSourceConfig   : 已设置连接保活时间为: 120000ms (2分钟)
2025-05-29 13:11:14.528  INFO 94282 --- [main] com.zaxxer.hikari.HikariDataSource       : PharmacyHikariCP - Starting...
2025-05-29 13:11:15.020  INFO 94282 --- [main] com.zaxxer.hikari.HikariDataSource       : PharmacyHikariCP - Start completed.
2025-05-29 13:11:15.045  INFO 94282 --- [main] c.p.management.config.DataSourceConfig   : 已初始化数据库连接并设置会话变量
2025-05-29 13:11:15.049  INFO 94282 --- [main] c.p.management.config.DataSourceConfig   : 当前max_allowed_packet值为: 67108864 bytes (64 MB)
2025-05-29 13:11:15.050  WARN 94282 --- [main] c.p.management.config.DataSourceConfig   : 当前max_allowed_packet值(64 MB)小于期望值(256 MB)，请在MySQL配置文件中设置 max_allowed_packet=256M，或使用SET GLOBAL max_allowed_packet=268435456命令设置。
2025-05-29 13:11:15.128  INFO 94282 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-29 13:11:15.177  INFO 94282 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.4.33
2025-05-29 13:11:15.307  INFO 94282 --- [main] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-05-29 13:11:15.398  INFO 94282 --- [main] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-05-29 13:11:17.252  INFO 94282 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-05-29 13:11:17.263  INFO 94282 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-29 13:11:18.043  WARN 94282 --- [main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-29 13:11:18.111  INFO 94282 --- [main] c.p.management.config.DataSourceConfig   : 数据库连接验证成功
2025-05-29 13:11:18.782  INFO 94282 --- [main] o.s.s.web.DefaultSecurityFilterChain     : Will not secure any request
2025-05-29 13:11:18.962 DEBUG 94282 --- [main] s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-05-29 13:11:19.036 DEBUG 94282 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : 91 mappings in 'requestMappingHandlerMapping'
2025-05-29 13:11:19.300 DEBUG 94282 --- [main] o.s.w.s.handler.SimpleUrlHandlerMapping  : Patterns [/webjars/**, /**, /uploads/**, /static/**, /css/**, /js/**, /img/**, /plugins/**, /dist/**, /build/**, /vendor/**, /uploads/avatars/**, /medical-records/**] in 'resourceHandlerMapping'
2025-05-29 13:11:19.314 DEBUG 94282 --- [main] .m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-05-29 13:11:19.480  INFO 94282 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-05-29 13:11:19.490  INFO 94282 --- [main] c.p.m.PharmacyManagementApplication      : Started PharmacyManagementApplication in 8.403 seconds (JVM running for 8.972)
2025-05-29 13:11:19.492 DEBUG 94282 --- [scheduling-1] c.p.m.config.DatabaseConnectionMonitor   : 数据库连接检查成功
2025-05-29 13:11:19.493  INFO 94282 --- [scheduling-1] c.p.m.config.DatabaseConnectionMonitor   : 已刷新数据库会话变量
2025-05-29 13:11:19.494 DEBUG 94282 --- [scheduling-1] c.p.m.config.DatabaseConnectionMonitor   : 当前max_allowed_packet值为: 67108864 bytes (64 MB)
2025-05-29 13:11:19.500  INFO 94282 --- [main] c.p.m.config.AdminPharmacyInitializer    : 开始初始化系统管理员药店关联...
2025-05-29 13:11:19.722 DEBUG 94282 --- [main] org.hibernate.SQL                        : 
    select
        pharmacy0_.id as id1_3_,
        pharmacy0_.address as address2_3_,
        pharmacy0_.business_hours as business3_3_,
        pharmacy0_.created_at as created_4_3_,
        pharmacy0_.description as descript5_3_,
        pharmacy0_.is_active as is_activ6_3_,
        pharmacy0_.license as license7_3_,
        pharmacy0_.name as name8_3_,
        pharmacy0_.phone as phone9_3_,
        pharmacy0_.updated_at as updated10_3_ 
    from
        pharmacies pharmacy0_
2025-05-29 13:11:19.753  INFO 94282 --- [main] c.p.m.config.AdminPharmacyInitializer    : 为系统管理员 admin 关联 2 个药店
2025-05-29 13:11:19.778 DEBUG 94282 --- [main] org.hibernate.SQL                        : 
    delete 
    from
        user_pharmacy 
    where
        user_id=?
2025-05-29 13:11:19.817 DEBUG 94282 --- [main] org.hibernate.SQL                        : 
    insert 
    into
        user_pharmacy
        (pharmacy_id, user_id) 
    values
        (?, ?)
2025-05-29 13:11:19.825 DEBUG 94282 --- [main] org.hibernate.SQL                        : 
    insert 
    into
        user_pharmacy
        (pharmacy_id, user_id) 
    values
        (?, ?)
2025-05-29 13:11:19.826  INFO 94282 --- [main] c.p.m.config.AdminPharmacyInitializer    : 系统管理员药店关联初始化完成
2025-05-29 13:11:41.686  INFO 94282 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-29 13:11:41.686  INFO 94282 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-05-29 13:11:41.687 DEBUG 94282 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Detected StandardServletMultipartResolver
2025-05-29 13:11:41.687 DEBUG 94282 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Detected AcceptHeaderLocaleResolver
2025-05-29 13:11:41.687 DEBUG 94282 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Detected FixedThemeResolver
2025-05-29 13:11:41.688 DEBUG 94282 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@45a05350
2025-05-29 13:11:41.688 DEBUG 94282 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Detected org.springframework.web.servlet.support.SessionFlashMapManager@71e55e48
2025-05-29 13:11:41.688 DEBUG 94282 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-05-29 13:11:41.688  INFO 94282 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-05-29 13:12:19.302  INFO 94282 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-29 13:12:19.305  INFO 94282 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : PharmacyHikariCP - Shutdown initiated...
2025-05-29 13:12:19.311  INFO 94282 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : PharmacyHikariCP - Shutdown completed.
2025-05-29 13:12:33.360  INFO 94633 --- [main] c.p.m.PharmacyManagementApplication      : Starting PharmacyManagementApplication using Java 1.8.0_421 on ZBMAC-061e5e91a with PID 94633 (/Users/<USER>/ai/pharmacy_online/pharmacy/target/classes started by liandahu in /Users/<USER>/ai/pharmacy_online/pharmacy)
2025-05-29 13:12:33.363 DEBUG 94633 --- [main] c.p.m.PharmacyManagementApplication      : Running with Spring Boot v2.5.14, Spring v5.3.20
2025-05-29 13:12:33.363  INFO 94633 --- [main] c.p.m.PharmacyManagementApplication      : The following 1 profile is active: "dev"
2025-05-29 13:12:33.832  INFO 94633 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-29 13:12:33.898  INFO 94633 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 61 ms. Found 8 JPA repository interfaces.
2025-05-29 13:12:34.266  INFO 94633 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@767191b1' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 13:12:34.270  INFO 94633 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 13:12:34.519  INFO 94633 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-05-29 13:12:34.527  INFO 94633 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-05-29 13:12:34.527  INFO 94633 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.63]
2025-05-29 13:12:34.632  INFO 94633 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-05-29 13:12:34.632  INFO 94633 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1207 ms
2025-05-29 13:12:34.671 DEBUG 94633 --- [main] o.s.w.s.r.ResourceUrlEncodingFilter      : Filter 'resourceUrlEncodingFilter' configured for use
2025-05-29 13:12:34.702  INFO 94633 --- [main] c.p.management.config.DataSourceConfig   : 已添加maxAllowedPacket参数到JDBC URL: ********************************************************************************************************************************************************************************
2025-05-29 13:12:34.703  INFO 94633 --- [main] c.p.management.config.DataSourceConfig   : 已调整连接池最大生命周期为: 600000ms (10分钟)
2025-05-29 13:12:34.703  INFO 94633 --- [main] c.p.management.config.DataSourceConfig   : 已设置连接超时时间为: 30000ms (30秒)
2025-05-29 13:12:34.703  INFO 94633 --- [main] c.p.management.config.DataSourceConfig   : 已设置空闲超时为: 120000ms (2分钟)
2025-05-29 13:12:34.703  INFO 94633 --- [main] c.p.management.config.DataSourceConfig   : 已设置连接测试查询
2025-05-29 13:12:34.704  INFO 94633 --- [main] c.p.management.config.DataSourceConfig   : 已设置连接保活时间为: 120000ms (2分钟)
2025-05-29 13:12:34.705  INFO 94633 --- [main] com.zaxxer.hikari.HikariDataSource       : PharmacyHikariCP - Starting...
2025-05-29 13:12:34.883  INFO 94633 --- [main] com.zaxxer.hikari.HikariDataSource       : PharmacyHikariCP - Start completed.
2025-05-29 13:12:34.886  INFO 94633 --- [main] c.p.management.config.DataSourceConfig   : 已初始化数据库连接并设置会话变量
2025-05-29 13:12:34.889  INFO 94633 --- [main] c.p.management.config.DataSourceConfig   : 当前max_allowed_packet值为: 67108864 bytes (64 MB)
2025-05-29 13:12:34.889  WARN 94633 --- [main] c.p.management.config.DataSourceConfig   : 当前max_allowed_packet值(64 MB)小于期望值(256 MB)，请在MySQL配置文件中设置 max_allowed_packet=256M，或使用SET GLOBAL max_allowed_packet=268435456命令设置。
2025-05-29 13:12:34.953  INFO 94633 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-29 13:12:34.990  INFO 94633 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.4.33
2025-05-29 13:12:35.083  INFO 94633 --- [main] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-05-29 13:12:35.156  INFO 94633 --- [main] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-05-29 13:12:35.755  INFO 94633 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-05-29 13:12:35.764  INFO 94633 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-29 13:12:36.577  WARN 94633 --- [main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-29 13:12:36.641  INFO 94633 --- [main] c.p.management.config.DataSourceConfig   : 数据库连接验证成功
2025-05-29 13:12:37.017  INFO 94633 --- [main] o.s.s.web.DefaultSecurityFilterChain     : Will not secure any request
2025-05-29 13:12:37.140 DEBUG 94633 --- [main] s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-05-29 13:12:37.199 DEBUG 94633 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : 91 mappings in 'requestMappingHandlerMapping'
2025-05-29 13:12:37.437 DEBUG 94633 --- [main] o.s.w.s.handler.SimpleUrlHandlerMapping  : Patterns [/webjars/**, /**, /uploads/**, /static/**, /css/**, /js/**, /img/**, /plugins/**, /dist/**, /build/**, /vendor/**, /uploads/avatars/**, /medical-records/**] in 'resourceHandlerMapping'
2025-05-29 13:12:37.452 DEBUG 94633 --- [main] .m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-05-29 13:12:37.596  INFO 94633 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-05-29 13:12:37.606  INFO 94633 --- [main] c.p.m.PharmacyManagementApplication      : Started PharmacyManagementApplication in 4.64 seconds (JVM running for 4.946)
2025-05-29 13:12:37.607 DEBUG 94633 --- [scheduling-1] c.p.m.config.DatabaseConnectionMonitor   : 数据库连接检查成功
2025-05-29 13:12:37.608  INFO 94633 --- [scheduling-1] c.p.m.config.DatabaseConnectionMonitor   : 已刷新数据库会话变量
2025-05-29 13:12:37.609 DEBUG 94633 --- [scheduling-1] c.p.m.config.DatabaseConnectionMonitor   : 当前max_allowed_packet值为: 67108864 bytes (64 MB)
2025-05-29 13:12:37.616  INFO 94633 --- [main] c.p.m.config.AdminPharmacyInitializer    : 开始初始化系统管理员药店关联...
2025-05-29 13:12:37.718 DEBUG 94633 --- [main] org.hibernate.SQL                        : 
    select
        pharmacy0_.id as id1_3_,
        pharmacy0_.address as address2_3_,
        pharmacy0_.business_hours as business3_3_,
        pharmacy0_.created_at as created_4_3_,
        pharmacy0_.description as descript5_3_,
        pharmacy0_.is_active as is_activ6_3_,
        pharmacy0_.license as license7_3_,
        pharmacy0_.name as name8_3_,
        pharmacy0_.phone as phone9_3_,
        pharmacy0_.updated_at as updated10_3_ 
    from
        pharmacies pharmacy0_
2025-05-29 13:12:37.750  INFO 94633 --- [main] c.p.m.config.AdminPharmacyInitializer    : 为系统管理员 admin 关联 2 个药店
2025-05-29 13:12:37.777 DEBUG 94633 --- [main] org.hibernate.SQL                        : 
    delete 
    from
        user_pharmacy 
    where
        user_id=?
2025-05-29 13:12:37.803 DEBUG 94633 --- [main] org.hibernate.SQL                        : 
    insert 
    into
        user_pharmacy
        (pharmacy_id, user_id) 
    values
        (?, ?)
2025-05-29 13:12:37.811 DEBUG 94633 --- [main] org.hibernate.SQL                        : 
    insert 
    into
        user_pharmacy
        (pharmacy_id, user_id) 
    values
        (?, ?)
2025-05-29 13:12:37.813  INFO 94633 --- [main] c.p.m.config.AdminPharmacyInitializer    : 系统管理员药店关联初始化完成
2025-05-29 13:13:00.155  INFO 94633 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-29 13:13:00.155  INFO 94633 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-05-29 13:13:00.156 DEBUG 94633 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Detected StandardServletMultipartResolver
2025-05-29 13:13:00.156 DEBUG 94633 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Detected AcceptHeaderLocaleResolver
2025-05-29 13:13:00.156 DEBUG 94633 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Detected FixedThemeResolver
2025-05-29 13:13:00.159 DEBUG 94633 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@7a482b3a
2025-05-29 13:13:00.160 DEBUG 94633 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Detected org.springframework.web.servlet.support.SessionFlashMapManager@4d7fba20
2025-05-29 13:13:00.160 DEBUG 94633 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-05-29 13:13:00.160  INFO 94633 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 4 ms
2025-05-29 13:13:00.178 DEBUG 94633 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : GET "/api/v1/test/health", parameters={}
2025-05-29 13:13:00.182 DEBUG 94633 --- [http-nio-8080-exec-1] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.pharmacy.management.api.TestController#health()
2025-05-29 13:13:00.204 DEBUG 94633 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed 302 FOUND
2025-05-29 13:13:32.780 DEBUG 94633 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet        : GET "/api/v1/test/health", parameters={}
2025-05-29 13:13:32.780 DEBUG 94633 --- [http-nio-8080-exec-2] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.pharmacy.management.api.TestController#health()
2025-05-29 13:13:32.781 DEBUG 94633 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet        : Completed 302 FOUND
2025-05-29 13:13:38.259  INFO 94633 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-29 13:13:38.260  INFO 94633 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : PharmacyHikariCP - Shutdown initiated...
2025-05-29 13:13:38.264  INFO 94633 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : PharmacyHikariCP - Shutdown completed.
