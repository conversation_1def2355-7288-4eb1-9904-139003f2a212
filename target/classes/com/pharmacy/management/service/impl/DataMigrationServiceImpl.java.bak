package com.pharmacy.management.service.impl;

import com.pharmacy.management.config.PharmacyContext;
import com.pharmacy.management.entity.Pharmacy;
import com.pharmacy.management.service.DataMigrationService;
import com.pharmacy.management.service.PatientService;
import com.pharmacy.management.service.QuickMedicineService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.*;
import java.util.*;

/**
 * 分析表结构和数据量
 */
private Map<String, Object> analyzeTable(Connection conn, String tableName) throws SQLException {
    Map<String, Object> tableInfo = new HashMap<>();
    
    try {
        // 获取表结构
        List<Map<String, Object>> columns = new ArrayList<>();
        try (Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery("PRAGMA table_info(" + tableName + ")")) {
            
            while (rs.next()) {
                Map<String, Object> column = new HashMap<>();
                column.put("name", rs.getString("name"));
                column.put("type", rs.getString("type"));
                column.put("notNull", rs.getBoolean("notnull"));
                column.put("primaryKey", rs.getBoolean("pk"));
                
                // 添加MySQL对应的字段名
                String mysqlFieldName = getMysqlFieldName(tableName, rs.getString("name"));
                column.put("mysqlField", mysqlFieldName);
                
                columns.add(column);
            }
        }
        tableInfo.put("columns", columns);
        
        // 获取数据量
        try (Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM " + tableName)) {
            
            if (rs.next()) {
                tableInfo.put("count", rs.getInt(1));
            }
        }
        
        // 添加MySQL对应的表名
        tableInfo.put("mysqlTable", getMysqlTableName(tableName));
        tableInfo.put("status", "success");
    } catch (SQLException e) {
        logger.error("分析表 " + tableName + " 失败", e);
        tableInfo.put("status", "error");
        tableInfo.put("errorMessage", "表 " + tableName + " 不存在或结构不匹配: " + e.getMessage());
        tableInfo.put("count", 0);
        tableInfo.put("columns", new ArrayList<>());
    }
    
    return tableInfo;
}

/**
 * 获取MySQL对应的表名
 */
private String getMysqlTableName(String sqliteTableName) {
    Map<String, String> tableMapping = new HashMap<>();
    tableMapping.put("patients", "patients");
    tableMapping.put("medical_records", "medical_records");
    tableMapping.put("quick_medicines", "quick_medicines");
    tableMapping.put("patient", "patients");
    tableMapping.put("medical_record", "medical_records");
    tableMapping.put("quick_medicine", "quick_medicines");
    
    return tableMapping.getOrDefault(sqliteTableName, sqliteTableName);
}

/**
 * 获取MySQL对应的字段名
 */
private String getMysqlFieldName(String tableName, String sqliteFieldName) {
    // 获取字段映射
    Map<String, Object> mappings = getFieldMappings();
    
    if ("patients".equals(tableName) || "patient".equals(tableName)) {
        @SuppressWarnings("unchecked")
        Map<String, String> patientMapping = (Map<String, String>) mappings.get("patientMapping");
        return patientMapping.getOrDefault(sqliteFieldName, sqliteFieldName);
    } else if ("medical_records".equals(tableName) || "medical_record".equals(tableName)) {
        @SuppressWarnings("unchecked")
        Map<String, String> recordMapping = (Map<String, String>) mappings.get("recordMapping");
        return recordMapping.getOrDefault(sqliteFieldName, sqliteFieldName);
    } else if ("quick_medicines".equals(tableName) || "quick_medicine".equals(tableName)) {
        @SuppressWarnings("unchecked")
        Map<String, String> medicineMapping = (Map<String, String>) mappings.get("medicineMapping");
        return medicineMapping.getOrDefault(sqliteFieldName, sqliteFieldName);
    }
    
    return sqliteFieldName;
}

/**
 * 获取SQLite数据库中的所有表
 */
private List<String> getSqliteTables(Connection conn) throws SQLException {
    List<String> tables = new ArrayList<>();
    try (Statement stmt = conn.createStatement();
         ResultSet rs = stmt.executeQuery("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")) {
        
        while (rs.next()) {
            tables.add(rs.getString("name"));
        }
    }
    return tables;
}

@Override
public Map<String, Object> analyzeSqliteFile(MultipartFile sqliteFile) throws Exception {
    logger.info("开始分析SQLite数据库文件");
    
    // 将上传的文件保存到临时目录
    Path tempFile = saveTempFile(sqliteFile);
    
    Map<String, Object> result = new HashMap<>();
    Connection conn = null;
    
    try {
        // 连接SQLite数据库
        conn = connectToSqliteDb(tempFile.toString());
        
        // 获取SQLite数据库中的所有表
        List<String> tables = getSqliteTables(conn);
        logger.info("SQLite数据库中的表: {}", tables);
        
        // 尝试分析患者表（可能有不同的表名）
        Map<String, Object> patientTableInfo = null;
        for (String tableName : Arrays.asList("patients", "patient", "Patient", "Patients")) {
            if (tables.contains(tableName)) {
                patientTableInfo = analyzeTable(conn, tableName);
                if ("success".equals(patientTableInfo.get("status"))) {
                    break;
                }
            }
        }
        if (patientTableInfo == null) {
            patientTableInfo = new HashMap<>();
            patientTableInfo.put("status", "error");
            patientTableInfo.put("errorMessage", "未找到患者表");
            patientTableInfo.put("count", 0);
            patientTableInfo.put("columns", new ArrayList<>());
        }
        result.put("patientTable", patientTableInfo);
        
        // 尝试分析医疗记录表（可能有不同的表名）
        Map<String, Object> medicalRecordTableInfo = null;
        for (String tableName : Arrays.asList("medical_records", "medical_record", "MedicalRecord", "MedicalRecords")) {
            if (tables.contains(tableName)) {
                medicalRecordTableInfo = analyzeTable(conn, tableName);
                if ("success".equals(medicalRecordTableInfo.get("status"))) {
                    break;
                }
            }
        }
        if (medicalRecordTableInfo == null) {
            medicalRecordTableInfo = new HashMap<>();
            medicalRecordTableInfo.put("status", "error");
            medicalRecordTableInfo.put("errorMessage", "未找到医疗记录表");
            medicalRecordTableInfo.put("count", 0);
            medicalRecordTableInfo.put("columns", new ArrayList<>());
        }
        result.put("medicalRecordTable", medicalRecordTableInfo);
        
        // 尝试分析快捷药品表（可能有不同的表名）
        Map<String, Object> quickMedicineTableInfo = null;
        for (String tableName : Arrays.asList("quick_medicines", "quick_medicine", "QuickMedicine", "QuickMedicines")) {
            if (tables.contains(tableName)) {
                quickMedicineTableInfo = analyzeTable(conn, tableName);
                if ("success".equals(quickMedicineTableInfo.get("status"))) {
                    break;
                }
            }
        }
        if (quickMedicineTableInfo == null) {
            quickMedicineTableInfo = new HashMap<>();
            quickMedicineTableInfo.put("status", "error");
            quickMedicineTableInfo.put("errorMessage", "未找到快捷药品表");
            quickMedicineTableInfo.put("count", 0);
            quickMedicineTableInfo.put("columns", new ArrayList<>());
        }
        result.put("quickMedicineTable", quickMedicineTableInfo);
        
        // 添加字段映射信息
        result.put("fieldMappings", getFieldMappings());
        
        // 添加SQLite文件名
        result.put("sqliteFileName", sqliteFile.getOriginalFilename());
        
        logger.info("SQLite数据库文件分析完成");
        return result;
    } finally {
        // 关闭连接
        if (conn != null) {
            try {
                conn.close();
            } catch (SQLException e) {
                logger.error("关闭SQLite连接失败", e);
            }
        }
        
        // 不删除临时文件，因为后续迁移还需要使用
    }
}